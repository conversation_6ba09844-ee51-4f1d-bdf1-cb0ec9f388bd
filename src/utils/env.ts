import { z } from 'zod'

const envSchema = z.object({
	BASE_URL: z.string().url(),
	DEV: z.boolean().default(false),
})

const _env = envSchema.safeParse({
	BASE_URL: import.meta.env.VITE_BACKEND_BASE_URL,
	DEV: import.meta.env.DEV,
})

if (!_env.success) {
	const errorTable = Object.entries(_env.error.flatten().fieldErrors).reduce(
		(acc, [key, value]) => {
			acc[key] = value.join(', ')
			return acc
		},
		{} as Record<string, string>,
	)

	throw new Error(`Invalid environment variables: \n${JSON.stringify(errorTable, null, 2)}`)
}

export const env = _env.data
