import { isAfter, isWithinInterval, parseISO } from 'date-fns'
import { useEffect, useRef, useState } from 'react'
import { useParams, useSearchParams } from 'react-router'
import { EChannelContentType } from '~/enums/channel'
import { EQueryKeys } from '~/enums/query-keys'
import type { IAgenda } from '~/interfaces/agenda'
import type { IChannelContent } from '~/interfaces/channel'
import { queryClient } from '~/providers/query-provider'
import { getChannelContent, useGetChannel, useGetChannelContent } from '~/services/channel'
import { delay } from '~/utils/delay'
import { devLog } from '~/utils/log'

const ANIMATION_CADENCE = 50 // px per second
const ANIMATION_PADDING = 48

export function useChannelScreenContainer() {
	const [currentAgenda, setCurrentAgenda] = useState<IAgenda | null>(null)

	const [currentContent, setCurrentContent] = useState<IChannelContent | null>(null)

	const [renderList, setRenderList] = useState<IAgenda[]>([])

	const [animationEnd, setAnimationEnd] = useState(0)

	const [animationDuration, setAnimationDuration] = useState(30)

	const [searchParams] = useSearchParams()

	const isSetupCompleted = useRef(false)

	const isMounted = useRef(true)

	const scrollContainerRef = useRef<HTMLDivElement>(null)

	const scrollContentRef = useRef<HTMLDivElement>(null)

	const dateParam = searchParams.get('date')

	const timeParam = searchParams.get('time')

	const { channelId } = useParams<{
		channelId: string
	}>()

	const { data: channel, isLoading: isLoadingChannel } = useGetChannel(channelId)

	const isAgenda = currentContent?.type === EChannelContentType.AGENDA

	const contentId = isAgenda ? currentContent?.swap_place_id || null : null

	const {
		data: content,
		isLoading: isLoadingContent,
		dataUpdatedAt: contentUpdatedAt,
	} = useGetChannelContent(channelId, contentId, dateParam)

	const now = dateParam ? new Date(dateParam) : new Date()

	if (timeParam) {
		const [hours, minutes] = timeParam.split(':')

		now.setHours(Number(hours))
		now.setMinutes(Number(minutes))
	}

	const isLoading = isLoadingContent || isLoadingChannel

	const isContentEmpty = !content?.length

	const isEndReached = !renderList.length && !isContentEmpty

	function verifyCurrentEvent(event: IAgenda) {
		const startDate = parseISO(event.beginsAt)

		const endDate = parseISO(event.endsAt)

		return isWithinInterval(now, {
			start: startDate,
			end: endDate,
		})
	}

	async function prefetchContent() {
		const agendaIds = new Set<string>()

		for (const event of channel?.items || []) {
			if (event.type === EChannelContentType.BANNER) continue

			agendaIds.add(event.swap_place_id)
		}

		for await (const agendaId of agendaIds) {
			devLog(`Prefetching content for ${agendaId}`)

			await queryClient.prefetchQuery({
				queryKey: [EQueryKeys.CHANNEL_CONTENT, channelId, agendaId, dateParam],
				queryFn: () => getChannelContent(channelId, agendaId, dateParam),
			})
		}
	}

	async function startContentLoop() {
		if (!channel || !isMounted.current) return

		const [firstItem, ...restItems] = channel.items || []

		if (!firstItem) return

		setCurrentContent(firstItem)

		await delay(firstItem.time * 1000)

		if (!isMounted.current) return

		for (const content of restItems) {
			if (!isMounted.current) return

			setCurrentContent(content)

			await delay(content.time * 1000)
			if (!isMounted.current) return
		}

		if (isMounted.current) {
			startContentLoop()
		}
	}

	function updateRenderList() {
		if (!content?.length) return

		const availableEvents: IAgenda[] = []

		for (const event of content) {
			const isEndReached = isAfter(now, parseISO(event.endsAt))

			const isCurrentEvent = verifyCurrentEvent(event)

			if (isEndReached || isCurrentEvent) continue

			availableEvents.push(event)
		}

		setRenderList(availableEvents)
	}

	function updateCurrentEvent() {
		for (const event of content || []) {
			const isCurrentEvent = verifyCurrentEvent(event)

			if (isCurrentEvent) {
				setCurrentAgenda(event)
				break
			}
		}
	}

	async function calcAnimationStates() {
		await delay(100)

		const content = scrollContentRef.current
		const container = scrollContainerRef.current

		if (!content || !container) return

		const contentHeight = content.scrollHeight
		const containerHeight = container.clientHeight

		if (contentHeight <= containerHeight) {
			setAnimationEnd(0)
			setAnimationDuration(30)
			return
		}

		const end = container.clientHeight - contentHeight - ANIMATION_PADDING * 2

		const animationDistance = Math.abs(end)
		const duration = Math.ceil(animationDistance / ANIMATION_CADENCE) // seconds

		setAnimationEnd(end)
		setAnimationDuration(duration)
	}

	function setupCarrouselContent() {
		if (isSetupCompleted.current || isLoadingChannel) return

		isSetupCompleted.current = true
		isMounted.current = true

		prefetchContent()
		startContentLoop()

		return () => {
			isSetupCompleted.current = false
			isMounted.current = false
		}
	}
	useEffect(setupCarrouselContent, [isLoadingChannel])

	function startObservers() {
		updateCurrentEvent()
		updateRenderList()
		calcAnimationStates()

		const currentEventInterval = setInterval(updateCurrentEvent, 1000 * 60) //1 minute

		const renderListInterval = setInterval(updateRenderList, 1000 * 60) //1 minute

		window.addEventListener('resize', calcAnimationStates)
		window.addEventListener('orientationchange', calcAnimationStates)

		return () => {
			clearInterval(currentEventInterval)
			clearInterval(renderListInterval)

			window.removeEventListener('resize', calcAnimationStates)
			window.removeEventListener('orientationchange', calcAnimationStates)
		}
	}
	useEffect(startObservers, [content, isLoadingContent, ANIMATION_CADENCE, ANIMATION_PADDING])

	return {
		currentAgenda,
		scrollContainerRef,
		renderList,
		config: channel,
		dataUpdatedAt: contentUpdatedAt,
		currentContent,
		animationEnd,
		animationDuration,
		scrollContentRef,
		isEndReached,
		isContentEmpty,
		isLoading,
		isAgenda,
	}
}
