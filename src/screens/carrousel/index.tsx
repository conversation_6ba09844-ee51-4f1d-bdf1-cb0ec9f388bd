import clsx from 'clsx'
import { format } from 'date-fns'
import ArrowDownRightIcon from '~/assets/icons/arrow-down-right.svg'
import DropShadowAsset from '~/assets/images/drop-shadow.svg'
import { Agenda } from '~/components/agenda'
import { Banner } from '~/components/banner'
import { Footer } from '~/components/footer'
import { useChannelScreenContainer } from './container'

export function CarrouselScreen() {
	const {
		currentAgenda,
		renderList,
		config,
		dataUpdatedAt,
		scrollContainerRef,
		scrollContentRef,
		animationEnd,
		animationDuration,
		isContentEmpty,
		isEndReached,
		isLoading,
		isAgenda,
		currentContent,
	} = useChannelScreenContainer()

	if (isLoading)
		return (
			<div className="flex h-screen w-screen flex-col items-center justify-center bg-slate-800">
				<div className="flex-1" />

				<Footer dataUpdatedAt={dataUpdatedAt} />
			</div>
		)

	function Content() {
		if (isContentEmpty) {
			return (
				<div className="flex flex-1 flex-col items-center justify-center gap-6">
					{config?.title && (
						<div className="flex h-12 items-center justify-center rounded-full bg-white px-5">
							<span className="text-base font-semibold text-black uppercase">
								{config.title}
							</span>
						</div>
					)}

					<p className="text-5xl font-semibold text-white">Não há eventos neste Canal</p>
				</div>
			)
		}

		if (isEndReached) {
			return (
				<div className="flex flex-1 flex-col items-center justify-center">
					<div className="mb-14 flex flex-col items-center justify-center gap-2">
						<div className="flex h-32 items-center justify-center gap-6 rounded-full bg-white px-10">
							<span className="font-barlow text-[80px] font-normal text-black uppercase">
								{format(new Date(), 'dd/MM')}
							</span>

							<span className="text-4xl">✅</span>
						</div>

						{config?.title && (
							<div className="flex h-12 items-center justify-center rounded-full bg-white px-5">
								<span className="text-base font-semibold text-black uppercase">
									{config.title}
								</span>
							</div>
						)}
					</div>

					<p className="mb-6 text-5xl font-semibold text-white">Nos vemos na próxima!</p>

					<p className="max-w-[640px] text-center text-2xl font-medium text-white">
						Chegamos ao fim de mais um evento. Não há mais palestras para o dia de hoje.
					</p>
				</div>
			)
		}

		if (isAgenda)
			return (
				<div
					ref={scrollContainerRef}
					className="relative flex w-full flex-1 flex-col gap-6 overflow-y-hidden p-12"
				>
					<DropShadowAsset className="absolute top-0 left-0 z-10 w-full" />

					<div
						ref={scrollContentRef}
						style={{
							animation: `scroll ${animationDuration}s linear infinite`,
						}}
						className={'flex flex-col gap-6'}
					>
						{renderList?.map((agenda) => <Agenda key={agenda.title} agenda={agenda} />)}
					</div>
				</div>
			)

		return null
	}

	return (
		<div
			className={clsx('flex h-screen w-screen flex-col items-center justify-center', {
				'bg-contain bg-center': !!config?.background,
				'bg-slate-800 bg-cover': !config?.background,
			})}
			style={
				config?.background
					? {
							backgroundImage: `url(${config?.background})`,
						}
					: undefined
			}
		>
			<main className="relative flex h-screen w-full flex-col overflow-hidden">
				<div className="relative">
					{config?.top_banner && (
						<img
							className="absolute top-0 left-0 h-full w-full object-cover"
							src={config.top_banner}
							alt=""
						/>
					)}

					<header className="bg-black/48 backdrop-blur-3xl">
						{config?.top_banner && (
							<div className="p-12">
								<img
									className="h-32 w-full rounded-lg object-cover"
									src={config?.top_banner}
									alt="banner"
								/>
							</div>
						)}

						{currentAgenda && (
							<div className="flex flex-col gap-8 border-t border-b border-white/25 p-12">
								<h2 className="font-barlow text-3xl font-semibold text-white uppercase">
									• Acontecendo agora
								</h2>

								<Agenda agenda={currentAgenda} />
							</div>
						)}

						{!!renderList.length && (
							<div className="flex items-center gap-2 px-12 py-8 text-white">
								<h2 className="font-barlow text-[2rem] font-semibold uppercase">
									Próximas palestras
								</h2>

								<ArrowDownRightIcon className="h-5 w-5" />
							</div>
						)}
					</header>
				</div>

				<Content />

				<Banner banner={currentContent} headerImg={config?.top_banner} />
			</main>

			<Footer dataUpdatedAt={dataUpdatedAt} />

			<style>
				{`
				@keyframes scroll {
					0% {
						transform: translateY(0px);
					}
					100% {
						transform: translateY(${animationEnd}px);
					}
				}
				`}
			</style>
		</div>
	)
}
