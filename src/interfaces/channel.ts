import type { EChannelContentType } from '~/enums/channel'

export interface IChannelListItem {
	id: number
	title: string
}

export interface IChannel {
	id: number
	swap_event_id: string
	title: string
	created_at: string
	updated_at: string
	top_banner: string
	background: string
	event_id: number
	items: IChannelContent[]
}

export interface IChannelContent {
	id: number
	channel_id: number
	type: EChannelContentType
	time: number
	media_url: string
	thumb: string
	order: number
	created_at: string
	updated_at: string
	swap_place_id: string
}
