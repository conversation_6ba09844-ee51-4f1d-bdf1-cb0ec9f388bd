import { type UseQueryResult, useQuery } from '@tanstack/react-query'
import { EQueryKeys } from '~/enums/query-keys'
import type { IEventListItem } from '~/interfaces/event'
import { api } from './api'

export function useGetEvents(): UseQueryResult<IEventListItem[]> {
	return useQuery({
		queryKey: [EQueryKeys.EVENTS],
		queryFn: async () => {
			const response = await api('/event/list')

			return response.data
		},
	})
}
