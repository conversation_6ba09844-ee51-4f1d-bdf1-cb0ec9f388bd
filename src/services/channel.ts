import { type UseQueryResult, useQuery } from '@tanstack/react-query'
import { format } from 'date-fns'
import { EQueryKeys } from '~/enums/query-keys'
import type { IAgenda } from '~/interfaces/agenda'
import type { IChannel, IChannelListItem } from '~/interfaces/channel'
import { api } from './api'

export function useGetChannels(eventId?: string): UseQueryResult<IChannelListItem[]> {
	return useQuery({
		queryKey: [EQueryKeys.CHANNELS, eventId],
		queryFn: async () => {
			const response = await api(`/event/get-channels/${eventId}`)

			return response.data
		},
		enabled: !!eventId,
	})
}

export function useGetChannel(channelId?: string): UseQueryResult<IChannel> {
	return useQuery({
		queryKey: [EQueryKeys.CHANNEL, channelId],
		queryFn: async () => {
			const response = await api(`/event/get-channel-content/${channelId}`)

			return response.data
		},
		enabled: !!channelId,
	})
}

export async function getChannelContent(
	channelId?: string,
	contentId?: string | null,
	mockDate?: string | null,
) {
	const date = mockDate ? mockDate.replaceAll('/', '-') : format(new Date(), 'yyyy-MM-dd')

	const response = await api<IAgenda[]>(
		`/event/get-plannings-by-place/${channelId}/${contentId}?date=${date}`,
	)

	return response.data
}

export function useGetChannelContent(
	channelId?: string,
	contentId?: string | null,
	mockDate?: string | null,
): UseQueryResult<IAgenda[]> {
	return useQuery({
		queryKey: [EQueryKeys.CHANNEL_CONTENT, channelId, contentId, mockDate],
		queryFn: () => getChannelContent(channelId, contentId, mockDate),
		enabled: !!channelId && !!contentId && !!mockDate,
	})
}
