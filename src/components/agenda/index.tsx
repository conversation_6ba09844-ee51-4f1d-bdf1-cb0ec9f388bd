import { format, parseISO } from 'date-fns'
import MegaphoneIcon from '~/assets/icons/megaphone.svg'
import UserIcon from '~/assets/icons/user-circle.svg'
import type { IAgendaProps } from './types'

export function Agenda({ agenda }: IAgendaProps) {
	const hour = format(parseISO(agenda.beginsAt), 'HH:mm')

	return (
		<div className="flex flex-col gap-8 rounded-2xl bg-white p-8 drop-shadow-2xl">
			<div className="flex gap-8">
				<span className="font-barlow text-7xl font-normal">{hour}</span>

				<div className="flex flex-1 flex-col gap-3">
					<p className="text-[2rem] font-medium">{agenda.title}</p>

					{/* <p className="font-normal text-base">
						{agenda.description}
					</p> */}
				</div>

				{/* <img
					className="h-32 w-40 rounded-lg object-cover"
					src={'agenda.thumb'}
					alt=""
				/> */}
			</div>

			{!!agenda.speakers.length && (
				<div className="flex items-center gap-7">
					<div className="flex items-center gap-2">
						<MegaphoneIcon className="h-6 w-6" />

						<span className="font-barlow text-xl font-medium uppercase">
							palestrantes
						</span>
					</div>

					<div className="grid grid-cols-3 gap-x-4">
						{agenda.speakers.map(({ firstName, lastName, photoUrl }) => {
							const fullName = [firstName, lastName].filter(Boolean).join(' ')

							return (
								<div className="flex items-center gap-2" key={fullName}>
									{photoUrl ? (
										<img
											className="h-9 w-9 rounded-full object-cover"
											src={photoUrl}
											alt={fullName}
										/>
									) : (
										<UserIcon className="h-9 w-9" />
									)}

									<span className="text-xl font-medium">{fullName}</span>
								</div>
							)
						})}
					</div>
				</div>
			)}
		</div>
	)
}
