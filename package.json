{"name": "informa-agenda-react", "private": true, "version": "0.0.0", "type": "module", "engines": {"node": ">=22.14.0", "pnpm": ">=9.15.4"}, "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "lint": "eslint . --fix", "test": "vitest", "test:watch": "vitest --watch", "test:cov": "vitest run --coverage", "test:ui": "vitest --ui", "prepare": "lefthook install"}, "dependencies": {"@tanstack/react-query": "^5.76.0", "@tanstack/react-query-devtools": "^5.76.0", "axios": "^1.8.2", "clsx": "^2.1.1", "date-fns": "^4.1.0", "react": "^19.0.0", "react-dom": "^19.0.0", "react-router": "^7.3.0", "zod": "^3.24.2"}, "devDependencies": {"@commitlint/cli": "^19.6.1", "@commitlint/config-conventional": "^19.6.0", "@eslint/js": "^9.27.0", "@semantic-release/changelog": "^6.0.3", "@semantic-release/commit-analyzer": "^13.0.1", "@semantic-release/git": "^10.0.1", "@semantic-release/github": "^11.0.1", "@semantic-release/npm": "^12.0.1", "@semantic-release/release-notes-generator": "^14.0.3", "@tailwindcss/vite": "^4.0.12", "@testing-library/dom": "^10.4.0", "@testing-library/react": "^16.1.0", "@types/node": "^20", "@types/react": "^19.0.10", "@types/react-dom": "^19.0.4", "@vitejs/plugin-react-swc": "^3.8.0", "@vitest/coverage-v8": "^3.0.8", "@vitest/ui": "^3.0.8", "eslint": "^9.27.0", "eslint-config-prettier": "^10.1.5", "eslint-plugin-prettier": "^5.4.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^15.15.0", "jsdom": "^25.0.1", "lefthook": "^1.10.1", "lint-staged": "^15.4.3", "prettier": "^3.5.3", "prettier-plugin-tailwindcss": "^0.6.11", "tailwindcss": "^4.0.12", "ts-node": "^10.9.2", "tsx": "^4.19.3", "typescript": "~5.7.2", "typescript-eslint": "^8.33.0", "vite": "^6.2.0", "vite-plugin-svgr": "^4.3.0", "vitest": "^3.0.8"}, "packageManager": "pnpm@9.15.4+sha512.b2dc20e2fc72b3e18848459b37359a32064663e5627a51e4c74b2c29dd8e8e0491483c3abb40789cfd578bf362fb6ba8261b05f0387d76792ed6e23ea3b1b6a0"}