name: Release

on:
  push:
    branches: [main]
  workflow_dispatch:

permissions:
  id-token: write
  contents: write
  issues: write
  pull-requests: write

jobs:
  generate-release:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Semantic Release
        uses: cycjimmy/semantic-release-action@v4
        env:
          GITHUB_TOKEN: ${{ secrets.GH_TOKEN }}