name: Deploy (PRD)

on:
  push:
    branches: [ci/prd]
  workflow_dispatch:

permissions:
  id-token: write
  contents: read

jobs:
  deploy-production:
    name: Build and Deploy to S3
    runs-on: ubuntu-latest
    env:
        VITE_BACKEND_BASE_URL: ${{ secrets.API_BASE_URL }}

    steps:
        - name: Checkout repository
          uses: actions/checkout@v3
          with:
            ref: main
            
        - name: Read .nvmrc
          id: nvm
          run: echo "node_version=$(cat .nvmrc)" >> $GITHUB_OUTPUT
  
        - name: Setup Node.js
          uses: actions/setup-node@v4
          with:
            node-version: ${{ steps.nvm.outputs.node_version }}
            
        - name: Install pnpm
          uses: pnpm/action-setup@v2
          with:
            version: 9.15.4
  
        - name: Install dependencies
          run: pnpm install
          
        - name: Build app
          run: pnpm build
  
        - name: Configure AWS credentials
          uses: aws-actions/configure-aws-credentials@v2
          with:
            aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
            aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
            aws-region: ${{ secrets.AWS_REGION }}
  
        - name: Sync to S3
          run: aws s3 sync dist s3://agenda-digital-front --delete
    
        - name: Invalidate CloudFront cache
          run: aws cloudfront create-invalidation --distribution-id E1MC2F5MUPDTMY --paths '/*'