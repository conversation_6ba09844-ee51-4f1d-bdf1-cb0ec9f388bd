# [1.7.0](https://github.com/Buildbox-IT-Solutions/agenda-digital-front/compare/v1.6.4...v1.7.0) (2025-05-20)


### Features

* add svg support ([e182552](https://github.com/Buildbox-IT-Solutions/agenda-digital-front/commit/e18255213f4691979c2eaf726c8a053fd46041fa))

## [1.6.4](https://github.com/Buildbox-IT-Solutions/agenda-digital-front/compare/v1.6.3...v1.6.4) (2025-05-14)


### Bug Fixes

* add mount control to prevent content loop calls after unmounting ([6987f5f](https://github.com/Buildbox-IT-Solutions/agenda-digital-front/commit/6987f5fad07d648ec3edae5119b8415c18876d6a))
* prefetch query querys ([03474d1](https://github.com/Buildbox-IT-Solutions/agenda-digital-front/commit/03474d1089d4ee7d8e2b84300f1e26622c97256c))

## [1.6.3](https://github.com/Buildbox-IT-Solutions/agenda-digital-front/compare/v1.6.2...v1.6.3) (2025-05-08)


### Bug Fixes

* prevent banner display during loading and adjust dependencies ([c7e65f3](https://github.com/Buildbox-IT-Solutions/agenda-digital-front/commit/c7e65f3b30abb3b9f73986c855e79d8ccd18c4eb))

## [1.6.2](https://github.com/Buildbox-IT-Solutions/agenda-digital-front/compare/v1.6.1...v1.6.2) (2025-04-22)


### Bug Fixes

* banner layout and adjust image handling ([95493d1](https://github.com/Buildbox-IT-Solutions/agenda-digital-front/commit/95493d1b2905289b93cea5e751b037a7a4d94513))
* correct initial banner display time in carrousel ([2ed7e60](https://github.com/Buildbox-IT-Solutions/agenda-digital-front/commit/2ed7e6068ea88afcad8d8550cda94f70926e257f))

## [1.6.1](https://github.com/Buildbox-IT-Solutions/agenda-digital-front/compare/v1.6.0...v1.6.1) (2025-04-22)


### Bug Fixes

* shadown above banner ([eb75c92](https://github.com/Buildbox-IT-Solutions/agenda-digital-front/commit/eb75c92539ca5fd3ff181362ef38c5126591fc82))

# [1.6.0](https://github.com/Buildbox-IT-Solutions/agenda-digital-front/compare/v1.5.0...v1.6.0) (2025-04-04)


### Features

* add user icon and drop shadow assets; enhance agenda layout ([e3ec2b2](https://github.com/Buildbox-IT-Solutions/agenda-digital-front/commit/e3ec2b26941ac6215f5bf89ab4b28d1f637fa145))

# [1.5.0](https://github.com/Buildbox-IT-Solutions/agenda-digital-front/compare/v1.4.2...v1.5.0) (2025-04-02)


### Features

* mock date by query param ([972afc0](https://github.com/Buildbox-IT-Solutions/agenda-digital-front/commit/972afc01808af12d1f4d3b503c2f52fa8eed49d5))

## [1.4.2](https://github.com/Buildbox-IT-Solutions/agenda-digital-front/compare/v1.4.1...v1.4.2) (2025-04-02)


### Bug Fixes

* render list end event date ([c53b24e](https://github.com/Buildbox-IT-Solutions/agenda-digital-front/commit/c53b24e527e30364aec05d0fb19a1268264b2850))

## [1.4.1](https://github.com/Buildbox-IT-Solutions/agenda-digital-front/compare/v1.4.0...v1.4.1) (2025-04-02)


### Bug Fixes

* some layout issues ([a4fe4c3](https://github.com/Buildbox-IT-Solutions/agenda-digital-front/commit/a4fe4c3ea83f7e9f664411d9629e9413c6f20a4a))

# [1.4.0](https://github.com/Buildbox-IT-Solutions/agenda-digital-front/compare/v1.3.0...v1.4.0) (2025-03-31)


### Features

* add ad banners ([4f5f104](https://github.com/Buildbox-IT-Solutions/agenda-digital-front/commit/4f5f1041ffd8ab1be6fc9442cd812573577ffb7f))
* footer component ([201bdae](https://github.com/Buildbox-IT-Solutions/agenda-digital-front/commit/201bdae7dbac27089296c1de22e7ea54fd31f45e))

# [1.3.0](https://github.com/Buildbox-IT-Solutions/agenda-digital-front/compare/v1.2.0...v1.3.0) (2025-03-31)


### Features

* **ci:** improve release action and deploy with pnpm ([8547743](https://github.com/Buildbox-IT-Solutions/agenda-digital-front/commit/854774346e565ccf02b9530141a377a58bcd3fa1))

# [1.2.0](https://github.com/Buildbox-IT-Solutions/agenda-digital-front/compare/v1.1.0...v1.2.0) (2025-03-27)


### Features

* adding action deploy ([cb04001](https://github.com/Buildbox-IT-Solutions/agenda-digital-front/commit/cb04001e370e4c55983cc2e96a67bf5c2c4bedb8))

# [1.1.0](https://github.com/Buildbox-IT-Solutions/agenda-digital-front/compare/v1.0.0...v1.1.0) (2025-03-25)


### Features

* channels scren ([540f315](https://github.com/Buildbox-IT-Solutions/agenda-digital-front/commit/540f31528331d989195c41418307ab26f464e00d))
* events scren ([082bf74](https://github.com/Buildbox-IT-Solutions/agenda-digital-front/commit/082bf7445927b72a412473ba6678b8676fc673a1))
* implemente channel config and agenda ([6bc4114](https://github.com/Buildbox-IT-Solutions/agenda-digital-front/commit/6bc4114bd43ee373c74c01ec5c2af0ba5f7a693c))

# 1.0.0 (2025-03-13)


### Bug Fixes

* event keys ([fea5608](https://github.com/Buildbox-IT-Solutions/agenda-digital-front/commit/fea56082f0113fa41fe7cacf6360a9c48f5b1a84))


### Features

* create carrousel screen ([001219b](https://github.com/Buildbox-IT-Solutions/agenda-digital-front/commit/001219b01a2e9f99d3ba6d6bcefefb3df7298644))
* create carrousel screen container ([88f9fe3](https://github.com/Buildbox-IT-Solutions/agenda-digital-front/commit/88f9fe337d638008ce565aacb818d6d683347696))
* first commit ([c601e03](https://github.com/Buildbox-IT-Solutions/agenda-digital-front/commit/c601e035cc6add962a273dfe4e3707ebba7f3b57))
